import FormHelperText from '@mui/joy/FormHelperText';
import { useMemo } from 'react';
import { getToConfig } from '@bika/contents/config/client/unit/to';
import type { ILocaleContext } from '@bika/contents/i18n';
import { ensureArray } from '@bika/types/database/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { type To, ToSchema, type ToType } from '@bika/types/unit/bo';
import { VariablesTextInput } from '@bika/ui/shared/types-form/variables-text-input';
import { Typography } from '@bika/ui/text-components';
import { EmailFieldBoSelect } from './email-field-bo-select';
import { MemberFieldBoSelect } from './member-field-bo-select';
import { MemberVariableSelect } from './member-variable-select';
import { RoleBoSelect } from './role-bo-select';
import { TeamBoSelect } from './team-bo-select';
import { Box } from '../../layout-components';
import { SelectInput } from '../../shared/types-form/select-input';

export interface ToSchemaBOInputProps {
  // 可以声明多少个类型，可隐藏一些不要的
  types: ToType[];
  value: To;
  onChange: (newVal: To) => void;
  locale: ILocaleContext;
  api?: INodeResourceApi;
  actionId?: string;
}

export function ToSchemaBOInput(props: ToSchemaBOInputProps) {
  const { locale, types, api, value, actionId } = props;
  const { t } = locale;
  const variables = api?.automation.getAutomationGlobalVariables(actionId);
  const typesOptions = useMemo(() => {
    const toConfig = getToConfig(locale);
    return types.map((type) => {
      const config = toConfig[type];
      return {
        label: config.label,
        value: type,
        description: config.description,
      };
    });
  }, [locale, types]);

  return (
    <>
      <SelectInput<ToType>
        options={typesOptions}
        value={value.type || types[0]}
        onChange={(newVal) => {
          if (!newVal) {
            return;
          }
          let val = { type: newVal } as To;
          if (newVal === 'EMAIL_FIELD' || newVal === 'MEMBER_FIELD') {
            val = {
              type: newVal,
              databaseId: '',
              viewId: '',
              fieldId: '',
            };
          }
          const clone = ToSchema.parse(val);
          props.onChange(clone);
        }}
        sx={{ width: 'fit-content', bgcolor: 'transparent', pl: 0.5 }}
      />

      <Box pl={0.5} pr={0.5}>
        {value.type === 'EMAIL_STRING' &&
          // 如果有声明types，且不包含当前的type，则不显示
          props.types.includes(value.type) && (
            <>
              <VariablesTextInput
                label={t.automation.action.find_members.to_email_addresses}
                automationVariables={variables}
                value={value.email}
                locale={locale}
                sx={{
                  minHeight: '40px',
                  paddingTop: '4px',
                  // marginBottom: '8px',
                }}
                onChange={(newVal) => {
                  props.onChange({
                    type: 'EMAIL_STRING',
                    email: newVal!,
                  });
                }}
              />
              <FormHelperText>{t.automation.action.find_members.to_email_addresses_description}</FormHelperText>
            </>
          )}
        {value.type === 'ADMIN' && api && (
          <Typography level="b2" sx={{ color: 'var(--text-primary)', marginTop: '4px' }}>
            {t.unit.to.admin_description}
          </Typography>
        )}
        {value.type === 'ALL_MEMBERS' && api && (
          <Typography level="b2" sx={{ color: 'var(--text-primary)', marginTop: '4px' }}>
            {t.unit.to.all_members_description}
          </Typography>
        )}
        {value.type === 'CURRENT_OPERATOR' && api && (
          <Typography level="b2" sx={{ color: 'var(--text-primary)', marginTop: '4px' }}>
            {t.unit.to.current_operator_description}
          </Typography>
        )}
        {value.type === 'USER' && (
          <VariablesTextInput
            label={t.unit.to.user_description}
            automationVariables={variables}
            value={value.userId}
            locale={locale}
            sx={{
              minHeight: '40px',
              paddingTop: '4px',
              // marginBottom: '8px',
            }}
            onChange={(newVal) => {
              props.onChange({
                type: 'USER',
                userId: newVal!,
              });
            }}
          />
        )}
        {value.type === 'UNIT_MEMBER' && api && (
          <MemberVariableSelect
            locale={locale}
            value={
              value.memberId
                ? {
                    type: 'SPECIFY_UNITS',
                    unitIds: ensureArray(value.memberId),
                  }
                : {
                    type: 'SPECIFY_UNITS',
                    unitIds: [],
                  }
            }
            onChange={(v) => {
              props.onChange({
                type: 'UNIT_MEMBER',
                memberId: v.unitIds,
              } as const);
            }}
            api={api}
            actionId={actionId}
          />
        )}

        {value.type === 'SPECIFY_UNITS' && api && (
          <MemberVariableSelect locale={locale} value={value} onChange={props.onChange} api={api} actionId={actionId} />
        )}
        {value.type === 'EMAIL_FIELD' && api && (
          <EmailFieldBoSelect value={value} onChange={props.onChange} locale={locale} api={api} />
        )}
        {/* {value.type === 'UNIT_MEMBER' && '选择成员'} */}
        {value.type === 'UNIT_ROLE' && (
          <RoleBoSelect
            value={value}
            onChange={props.onChange}
            locale={locale}
            api={api?.unit}
            label={t.unit.to.role_select_label}
          />
        )}
        {value.type === 'UNIT_TEAM' && (
          <TeamBoSelect
            value={value}
            onChange={props.onChange}
            locale={locale}
            api={api!.unit}
            label={t.unit.to.team_select_label}
          />
        )}
        {value.type === 'MEMBER_FIELD' && api && (
          <MemberFieldBoSelect
            locale={locale}
            value={value}
            onChange={props.onChange}
            api={api}
            label={t.unit.to.member_field}
          />
        )}
      </Box>
    </>
  );
}
