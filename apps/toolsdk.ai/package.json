{"name": "@toolsdk.ai/web", "version": "1.9.2-alpha.39", "private": true, "scripts": {"dev": "USE_TURBOPACK=true  next dev --turbopack --port 3002", "build": "NODE_OPTIONS=--max-old-space-size=8192 next build && npm run _build-prisma", "_build-prisma": "mkdir -p ./.next/standalone/node_modules/.prisma/client && cp -r ../../packages/@toolsdk.ai/orm/prisma/prisma-client/* ./.next/standalone/node_modules/.prisma/client", "test": "vitest", "start": "next start", "lint": "next lint"}, "dependencies": {"@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@clerk/nextjs": "^6.5.0", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@next/mdx": "15.2.3", "@toolsdk.ai/domain": "workspace:*", "@toolsdk.ai/mcp-server": "workspace:*", "@toolsdk.ai/orm": "workspace:*", "@toolsdk.ai/sdk-ts": "workspace:*", "@types/mdx": "^2.0.10", "axios": "^1.9.0", "basenext": "workspace:*", "live-plugin-manager": "^1.0.0", "next": "15.3.3", "random-words": "^2.0.1", "react": "18.3.1", "react-dom": "18.3.1", "sharelib": "workspace:*", "zod": "^3.24.5"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.3", "msw": "^2.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^3.2.4"}}